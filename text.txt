bg-witg simple : with
bg dark : gray-900
bg active : gold meduim 
bg blue on data simple bg-gray-300/40 text-gray-700 dark:bg-gray-800/50 
text for error this bg-red-100 text-red-700



 <div
        id="qr-export-template"
        className="fixed -top-[9999px] left-0 w-[450px] h-[600px] bg-gradient-to-br from-gold-mediem via-gold-mediem to-gold-mediem text-white"
        style={{ fontFamily: "Arial, sans-serif" }}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-yellow-400 to-orange-400 px-6 py-4 rounded-t-xl">
          <h1 className="text-gray-100 font-bold text-xl text-center">
            CHECK IN/OUT
          </h1>
        </div>
        <div className="absolute inset-0 pt-16">
          {/* Decorative circles */}
          <div className="absolute top-20 left-8 w-8 h-8 bg-gray-600 rounded-full opacity-30"></div>
          <div className="absolute top-32 left-16 w-6 h-6 bg-gray-600 rounded-full opacity-40"></div>
          <div className="absolute top-44 left-12 w-4 h-4 bg-gray-600 rounded-full opacity-50"></div>
          <div className="absolute top-20 right-8 w-6 h-6 bg-gray-600 rounded-full opacity-40"></div>
          <div className="absolute top-36 right-12 w-8 h-8 bg-gray-600 rounded-full opacity-30"></div>
          <div className="absolute top-52 right-16 w-5 h-5 bg-gray-600 rounded-full opacity-45"></div>
          <div className="absolute bottom-40 left-6 w-7 h-7 bg-gray-600 rounded-full opacity-35"></div>
          <div className="absolute bottom-56 left-20 w-5 h-5 bg-gray-600 rounded-full opacity-40"></div>
          <div className="absolute bottom-32 right-10 w-6 h-6 bg-gray-600 rounded-full opacity-30"></div>
          <div className="absolute bottom-48 right-6 w-4 h-4 bg-gray-600 rounded-full opacity-50"></div>
        </div>

        {/* Main Content */}
        <div className="relative z-10 flex flex-col items-center justify-center px-8 pt-12 pb-8">
          <div className="relative mb-10">
            <div className="w-96 h-96 bg-gray-100/50 rounded-full flex items-center justify-center shadow-2xl">
              <div className="w-48 h-48 bg-white rounded-lg flex items-center justify-center">
                {selectedQR && (
                  <QRCodeCanvas
                    value={selectedQR}
                    size={240}
                    level="M"
                    includeMargin={false}
                  />
                )}
              </div>
            </div>
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 px-4 py-1 rounded-full">
              <span className="text-white text-sm font-semibold">
                Branch - {branchname}
              </span>
            </div>
          </div>

          {/* Footer */}
          <div className="absolute bottom-3 left-0 right-0 flex justify-between items-center px-8 text-sm text-white/80">
            <div className="flex items-center">
              <span className="mr-2">Powered by</span>
              <div className="flex items-center">
                <span className="font-semibold">Vontex Dev</span>
              </div>
            </div>
            <div className="flex justify-between items-start p-4 text-sm">
              <div className="text-white/90">
                {new Date().toLocaleDateString("en-US", {
                  month: "2-digit",
                  day: "2-digit",
                  year: "2-digit",
                })}
                ,{" "}
                {new Date().toLocaleTimeString("en-US", {
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: true,
                })}
              </div>
            </div>
          </div>
        </div>
      </div>