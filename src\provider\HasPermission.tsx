import React from "react";

interface Permission {
  name: string;
}
interface Role {
  name: string;
  permissions: Permission[];
}
interface User {
  roles: Role[];
}
interface UserData {
  user: User;
}

const HasPermission = (requiredPermissions: string[]): boolean => {
  try {
    const userString = localStorage.getItem("user");
    if (!userString) return false;
    const userData: UserData = JSON.parse(userString);

    const { user } = userData;
    if (!user || !user.roles) return false;
    //extact permission from role user
    const userPermissions = user.roles.flatMap((role) =>
      role.permissions.map((permission) => permission.name.toLowerCase())
    );

    return requiredPermissions.every((permission) =>
      userPermissions.includes(permission.toLowerCase())
    );
  } catch (error) {
    console.error("Error checking permissions:", error);
    return false;
  }
};

export default HasPermission;
