# Leaflet.js Quick Start Guide

A comprehensive step-by-step guide to get you started with Leaflet basics.

## Table of Contents

1. [Setting up Leaflet](#1-setting-up-leaflet)
2. [Creating Your First Map](#2-creating-your-first-map)
3. [Working with Markers](#3-working-with-markers)
4. [Adding Popups](#4-adding-popups)
5. [Drawing Polylines](#5-drawing-polylines)
6. [Handling Events](#6-handling-events)
7. [Advanced Features](#7-advanced-features)

## 1. Setting up Leaflet

### Option A: CDN (Recommended for beginners)

```html
<!DOCTYPE html>
<html>
  <head>
    <title>My Leaflet Map</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Leaflet CSS -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css"
    />

    <style>
      #map {
        height: 400px;
        width: 100%;
      }
    </style>
  </head>
  <body>
    <div id="map"></div>

    <!-- Leaflet JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js"></script>
  </body>
</html>
```

### Option B: React/TypeScript Setup

```typescript
// Install: npm install leaflet @types/leaflet

import { useEffect, useRef } from "react";

// Load Leaflet dynamically
useEffect(() => {
  // Load CSS
  const link = document.createElement("link");
  link.rel = "stylesheet";
  link.href =
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css";
  document.head.appendChild(link);

  // Load JS
  const script = document.createElement("script");
  script.src =
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js";
  script.onload = () => setMapLoaded(true);
  document.head.appendChild(script);
}, []);
```

## 2. Creating Your First Map

### Basic Map Setup

```javascript
// Initialize the map
var map = L.map("map").setView([51.505, -0.09], 13);

// Add OpenStreetMap tiles
L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
  attribution: "© OpenStreetMap contributors",
}).addTo(map);
```

### Map Options

```javascript
var map = L.map("map", {
  center: [51.505, -0.09],
  zoom: 13,
  zoomControl: true,
  scrollWheelZoom: true,
  doubleClickZoom: true,
  dragging: true,
});
```

### Alternative Tile Layers

```javascript
// Satellite imagery
L.tileLayer(
  "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
  {
    attribution: "Tiles © Esri",
  }
).addTo(map);

// Dark theme
L.tileLayer("https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png", {
  attribution: "© OpenStreetMap contributors © CARTO",
}).addTo(map);
```

## 3. Working with Markers

### Basic Marker

```javascript
var marker = L.marker([51.5, -0.09]).addTo(map);
```

### Custom Icon Marker

```javascript
var customIcon = L.icon({
  iconUrl: "path/to/icon.png",
  iconSize: [32, 32],
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
});

var marker = L.marker([51.5, -0.09], { icon: customIcon }).addTo(map);
```

### Colored Markers

```javascript
// Using external colored marker icons
var redIcon = L.icon({
  iconUrl:
    "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png",
  shadowUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
});

var redMarker = L.marker([51.5, -0.09], { icon: redIcon }).addTo(map);
```

### Multiple Markers

```javascript
var cities = [
  { name: "London", coords: [51.505, -0.09] },
  { name: "Paris", coords: [48.8566, 2.3522] },
  { name: "Berlin", coords: [52.52, 13.405] },
];

cities.forEach(function (city) {
  L.marker(city.coords).addTo(map).bindPopup(city.name);
});
```

## 4. Adding Popups

### Basic Popup

```javascript
var marker = L.marker([51.5, -0.09])
  .addTo(map)
  .bindPopup("Hello World!")
  .openPopup();
```

### HTML Content in Popups

```javascript
var popupContent = `
    <div>
        <h3>London</h3>
        <p>Capital of England</p>
        <img src="london.jpg" width="200">
        <br>
        <button onclick="alert('Hello!')">Click me</button>
    </div>
`;

marker.bindPopup(popupContent);
```

### Popup Options

```javascript
marker.bindPopup("Hello World!", {
  maxWidth: 300,
  minWidth: 100,
  maxHeight: 200,
  closeButton: true,
  autoClose: false,
  closeOnClick: false,
});
```

### Standalone Popups

```javascript
var popup = L.popup()
  .setLatLng([51.5, -0.09])
  .setContent("I am a standalone popup.")
  .openOn(map);
```

## 5. Drawing Polylines

### Basic Polyline

```javascript
var polyline = L.polyline(
  [
    [51.509, -0.08],
    [51.503, -0.06],
    [51.51, -0.047],
  ],
  { color: "red" }
).addTo(map);

// Fit map to polyline bounds
map.fitBounds(polyline.getBounds());
```

### Styled Polylines

```javascript
var polyline = L.polyline(coordinates, {
  color: "blue",
  weight: 5,
  opacity: 0.7,
  dashArray: "10, 10",
  lineJoin: "round",
}).addTo(map);
```

### Polygons

```javascript
var polygon = L.polygon(
  [
    [51.509, -0.08],
    [51.503, -0.06],
    [51.51, -0.047],
  ],
  {
    color: "red",
    fillColor: "#f03",
    fillOpacity: 0.5,
  }
).addTo(map);
```

### Circles

```javascript
var circle = L.circle([51.508, -0.11], {
  color: "red",
  fillColor: "#f03",
  fillOpacity: 0.5,
  radius: 500,
}).addTo(map);
```

## 6. Handling Events

### Map Events

```javascript
// Click event
map.on("click", function (e) {
  console.log("You clicked the map at " + e.latlng);

  // Add marker at clicked location
  L.marker(e.latlng)
    .addTo(map)
    .bindPopup("You clicked here: " + e.latlng.toString());
});

// Zoom event
map.on("zoomend", function () {
  console.log("Current zoom level: " + map.getZoom());
});

// Move event
map.on("moveend", function () {
  console.log("Map center: " + map.getCenter());
});
```

### Marker Events

```javascript
var marker = L.marker([51.5, -0.09]).addTo(map);

marker.on("click", function (e) {
  console.log("Marker clicked!");
});

marker.on("mouseover", function (e) {
  this.openPopup();
});

marker.on("mouseout", function (e) {
  this.closePopup();
});
```

### Drag Events

```javascript
var draggableMarker = L.marker([51.5, -0.09], {
  draggable: true,
}).addTo(map);

draggableMarker.on("dragend", function (e) {
  var position = e.target.getLatLng();
  console.log("Marker moved to: " + position);
});
```

## 7. Advanced Features

### Layer Groups

```javascript
var cities = L.layerGroup([
  L.marker([51.5, -0.09]).bindPopup("London"),
  L.marker([48.8566, 2.3522]).bindPopup("Paris"),
]);

var restaurants = L.layerGroup([
  L.marker([51.51, -0.08]).bindPopup("Restaurant 1"),
  L.marker([51.49, -0.1]).bindPopup("Restaurant 2"),
]);

// Layer control
var overlayMaps = {
  Cities: cities,
  Restaurants: restaurants,
};

L.control.layers(null, overlayMaps).addTo(map);
```

### GeoJSON

```javascript
var geojsonFeature = {
  type: "Feature",
  properties: {
    name: "Coors Field",
    amenity: "Baseball Stadium",
  },
  geometry: {
    type: "Point",
    coordinates: [-104.99404, 39.75621],
  },
};

L.geoJSON(geojsonFeature).addTo(map);
```

### Custom Controls

```javascript
var info = L.control();

info.onAdd = function (map) {
  this._div = L.DomUtil.create("div", "info");
  this.update();
  return this._div;
};

info.update = function (props) {
  this._div.innerHTML =
    "<h4>Custom Info</h4>" +
    (props ? "<b>" + props.name + "</b>" : "Hover over a feature");
};

info.addTo(map);
```

## 8. Practical Examples

### Complete Interactive Map Example

```html
<!DOCTYPE html>
<html>
  <head>
    <title>Interactive Map</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css"
    />
    <style>
      #map {
        height: 500px;
      }
      .info {
        padding: 6px 8px;
        background: white;
        border-radius: 5px;
      }
    </style>
  </head>
  <body>
    <div id="map"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js"></script>
    <script>
      // Initialize map
      var map = L.map("map").setView([40.7128, -74.006], 10);

      // Add tile layer
      L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
        attribution: "© OpenStreetMap contributors",
      }).addTo(map);

      // Add markers with popups
      var locations = [
        { name: "Times Square", coords: [40.758, -73.9855], type: "tourist" },
        { name: "Central Park", coords: [40.7829, -73.9654], type: "park" },
        {
          name: "Brooklyn Bridge",
          coords: [40.7061, -73.9969],
          type: "landmark",
        },
      ];

      locations.forEach(function (location) {
        var icon = getIconByType(location.type);
        L.marker(location.coords, { icon: icon })
          .addTo(map)
          .bindPopup(`<b>${location.name}</b><br>Type: ${location.type}`);
      });

      // Custom icons function
      function getIconByType(type) {
        var colors = {
          tourist: "red",
          park: "green",
          landmark: "blue",
        };

        return L.icon({
          iconUrl: `https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-${colors[type]}.png`,
          shadowUrl:
            "https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png",
          iconSize: [25, 41],
          iconAnchor: [12, 41],
          popupAnchor: [1, -34],
          shadowSize: [41, 41],
        });
      }

      // Click to add marker
      map.on("click", function (e) {
        var marker = L.marker(e.latlng).addTo(map);
        marker.bindPopup(
          `<b>New Location</b><br>Lat: ${e.latlng.lat.toFixed(
            4
          )}<br>Lng: ${e.latlng.lng.toFixed(4)}`
        );
      });
    </script>
  </body>
</html>
```

### React Component Example

```typescript
import React, { useEffect, useRef, useState } from "react";

interface MapProps {
  center: [number, number];
  zoom: number;
  markers?: Array<{ lat: number; lng: number; popup: string }>;
}

const LeafletMap: React.FC<MapProps> = ({ center, zoom, markers = [] }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  // Load Leaflet
  useEffect(() => {
    if (window.L) {
      setMapLoaded(true);
      return;
    }

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href =
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css";
    document.head.appendChild(link);

    const script = document.createElement("script");
    script.src =
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js";
    script.onload = () => setMapLoaded(true);
    document.head.appendChild(script);
  }, []);

  // Initialize map
  useEffect(() => {
    if (!mapLoaded || !mapRef.current || mapInstanceRef.current) return;

    const map = window.L.map(mapRef.current).setView(center, zoom);

    window.L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(map);

    mapInstanceRef.current = map;

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [mapLoaded, center, zoom]);

  // Add markers
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    markers.forEach((marker) => {
      window.L.marker([marker.lat, marker.lng])
        .addTo(mapInstanceRef.current)
        .bindPopup(marker.popup);
    });
  }, [markers]);

  return <div ref={mapRef} style={{ height: "400px", width: "100%" }} />;
};

export default LeafletMap;
```

## 9. Common Patterns & Best Practices

### 1. Responsive Maps

```css
.map-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.map-container #map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
```

### 2. Loading States

```javascript
// Show loading indicator
function showLoading() {
  document.getElementById("loading").style.display = "block";
}

// Hide loading indicator
function hideLoading() {
  document.getElementById("loading").style.display = "none";
}

// Use with async operations
showLoading();
fetch("/api/markers")
  .then((response) => response.json())
  .then((data) => {
    addMarkersToMap(data);
    hideLoading();
  });
```

### 3. Error Handling

```javascript
// Tile layer with error handling
var tileLayer = L.tileLayer(
  "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
  {
    attribution: "© OpenStreetMap contributors",
  }
);

tileLayer.on("tileerror", function (error) {
  console.warn("Tile loading failed:", error);
  // Switch to fallback tile server
  var fallback = L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png");
  map.removeLayer(tileLayer);
  fallback.addTo(map);
});

tileLayer.addTo(map);
```

### 4. Performance Optimization

```javascript
// Use marker clustering for many markers
// Include: https://unpkg.com/leaflet.markercluster/dist/leaflet.markercluster.js

var markers = L.markerClusterGroup();

locations.forEach(function (location) {
  var marker = L.marker(location.coords);
  markers.addLayer(marker);
});

map.addLayer(markers);
```

### 5. Search Functionality

```javascript
// Simple geocoding search
async function searchLocation(query) {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
        query
      )}`
    );
    const results = await response.json();

    if (results.length > 0) {
      const result = results[0];
      const lat = parseFloat(result.lat);
      const lon = parseFloat(result.lon);

      map.setView([lat, lon], 15);
      L.marker([lat, lon])
        .addTo(map)
        .bindPopup(result.display_name)
        .openPopup();
    }
  } catch (error) {
    console.error("Search failed:", error);
  }
}

// Usage
document.getElementById("search-btn").addEventListener("click", function () {
  const query = document.getElementById("search-input").value;
  searchLocation(query);
});
```

## 10. Troubleshooting Common Issues

### Issue 1: Map not displaying

```javascript
// Solution: Ensure container has height
#map { height: 400px; }

// Or call invalidateSize after showing hidden map
setTimeout(() => map.invalidateSize(), 100);
```

### Issue 2: Tiles not loading

```javascript
// Check network connectivity and try alternative tile server
L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png", {
  attribution: "© OpenStreetMap contributors",
}).addTo(map);
```

### Issue 3: Markers not showing

```javascript
// Ensure coordinates are numbers, not strings
var lat = parseFloat(latString);
var lng = parseFloat(lngString);
L.marker([lat, lng]).addTo(map);
```

### Issue 4: Icons not loading

```javascript
// Use absolute URLs for custom icons
var icon = L.icon({
  iconUrl: "https://example.com/icon.png", // Not './icon.png'
  iconSize: [25, 41],
});
```

## 11. Resources & Next Steps

### Official Documentation

- [Leaflet Documentation](https://leafletjs.com/reference.html)
- [Leaflet Tutorials](https://leafletjs.com/examples.html)

### Useful Plugins

- **Leaflet.markercluster** - Marker clustering
- **Leaflet.draw** - Drawing tools
- **Leaflet.routing** - Routing and directions
- **Leaflet.heat** - Heatmap layer
- **Leaflet.fullscreen** - Fullscreen control

### Free Tile Providers

- OpenStreetMap (default)
- CartoDB
- Stamen Maps
- Esri World Imagery

### Advanced Topics to Explore

- Custom tile layers
- WebGL rendering
- Real-time data integration
- Mobile optimization
- Offline maps

This comprehensive guide covers everything you need to get started with Leaflet and build interactive maps. Start with the basics and gradually explore more advanced features as your needs grow!
