import React, { useState, useRef, useEffect } from "react";

interface TooltipProps {
  content: string;
  children: React.ReactNode;
  style?:
    | "light"
    | "dark"
    | "primary"
    | "success"
    | "warning"
    | "danger"
    | "info"
    | "purple"
    | "pink"
    | "orange";
  position?: "top" | "bottom" | "left" | "right";
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  style = "dark",
  position = "bottom",
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const buttonRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const updatePosition = () => {
    if (buttonRef.current && tooltipRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();

      let top = 0;
      let left = 0;

      switch (position) {
        case "top":
          top = buttonRect.top - tooltipRect.height - 8;
          left = buttonRect.left + (buttonRect.width - tooltipRect.width) / 2;
          break;
        case "bottom":
          top = buttonRect.bottom + 8;
          left = buttonRect.left + (buttonRect.width - tooltipRect.width) / 2;
          break;
        case "left":
          top = buttonRect.top + (buttonRect.height - tooltipRect.height) / 2;
          left = buttonRect.left - tooltipRect.width - 8;
          break;
        case "right":
          top = buttonRect.top + (buttonRect.height - tooltipRect.height) / 2;
          left = buttonRect.right + 8;
          break;
      }

      setTooltipPosition({ top, left });
    }
  };

  useEffect(() => {
    if (isVisible) {
      updatePosition();
      window.addEventListener("scroll", updatePosition);
      window.addEventListener("resize", updatePosition);

      return () => {
        window.removeEventListener("scroll", updatePosition);
        window.removeEventListener("resize", updatePosition);
      };
    }
  }, [isVisible, position]);

  const handleMouseEnter = () => {
    setIsVisible(true);
  };

  const handleMouseLeave = () => {
    setIsVisible(false);
  };

  // Color configurations for different styles
  const tooltipStyles = {
    light: {
      classes: "text-gray-900 bg-white border border-gray-200 shadow-lg",
      arrowColor: "#ffffff",
      borderColor: "#e5e7eb",
    },
    dark: {
      classes: "text-white bg-gray-900 shadow-lg",
      arrowColor: "#111827",
      borderColor: "#111827",
    },
    primary: {
      classes: "text-white bg-blue-600 shadow-lg",
      arrowColor: "#2563eb",
      borderColor: "#2563eb",
    },
    success: {
      classes: "text-white bg-green-600 shadow-lg",
      arrowColor: "#16a34a",
      borderColor: "#16a34a",
    },
    warning: {
      classes: "text-white bg-yellow-500 shadow-lg",
      arrowColor: "#eab308",
      borderColor: "#eab308",
    },
    danger: {
      classes: "text-white bg-red-600 shadow-lg",
      arrowColor: "#dc2626",
      borderColor: "#dc2626",
    },
    info: {
      classes: "text-white bg-cyan-600 shadow-lg",
      arrowColor: "#0891b2",
      borderColor: "#0891b2",
    },
    purple: {
      classes: "text-white bg-purple-600 shadow-lg",
      arrowColor: "#9333ea",
      borderColor: "#9333ea",
    },
    pink: {
      classes: "text-white bg-pink-600 shadow-lg",
      arrowColor: "#db2777",
      borderColor: "#db2777",
    },
    orange: {
      classes: "text-white bg-orange-600 shadow-lg",
      arrowColor: "#ea580c",
      borderColor: "#ea580c",
    },
  };

  const currentStyle = tooltipStyles[style];

  const getArrowStyle = () => {
    const arrowSize = 6;
    const baseStyle = {
      position: "absolute" as const,
      width: 0,
      height: 0,
    };

    switch (position) {
      case "top":
        return {
          ...baseStyle,
          bottom: `-${arrowSize}px`,
          left: "50%",
          transform: "translateX(-50%)",
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderTop: `${arrowSize}px solid ${currentStyle.arrowColor}`,
        };
      case "bottom":
        return {
          ...baseStyle,
          top: `-${arrowSize}px`,
          left: "50%",
          transform: "translateX(-50%)",
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid ${currentStyle.arrowColor}`,
        };
      case "left":
        return {
          ...baseStyle,
          right: `-${arrowSize}px`,
          top: "50%",
          transform: "translateY(-50%)",
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderLeft: `${arrowSize}px solid ${currentStyle.arrowColor}`,
        };
      case "right":
        return {
          ...baseStyle,
          left: `-${arrowSize}px`,
          top: "50%",
          transform: "translateY(-50%)",
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid ${currentStyle.arrowColor}`,
        };
      default:
        return baseStyle;
    }
  };

  return (
    <>
      <div
        ref={buttonRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="inline-block"
      >
        {children}
      </div>

      {isVisible && (
        <div
          ref={tooltipRef}
          role="tooltip"
          className={`fixed z-50 px-3 py-2 text-sm font-medium rounded-lg transition-opacity duration-200 ${currentStyle.classes}`}
          style={{
            top: `${tooltipPosition.top}px`,
            left: `${tooltipPosition.left}px`,
          }}
        >
          {content}
          <div style={getArrowStyle()} />
        </div>
      )}
    </>
  );
};
export default Tooltip;
