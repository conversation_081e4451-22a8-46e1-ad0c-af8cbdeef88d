import React from "react";
import ThemeToggleTwo from "../../components/common/ThemeToggleTwo";
import SigninForm from "./SigninForm";
export default function AuthLayout() {
  return (
    <div className="relative p-6 bg-white z-1 dark:bg-gray-900 sm:p-0">
      <div className="relative flex lg:flex-row w-screen h-screen justify-center flex-col dark:bg-gray-900 sm:p-0 ">
        <div className="flex flex-col flex-1 lg:w-1/2 w-full ">
          <SigninForm />
        </div>
        <div className="lg:w-1/2 w-full h-full bg-gray-900 dark:bg-gray-900 lg:grid items-center hidden">
          <div className=" relative items-center justify-center flex z-1">
            <div className="flex flex-col items-center max-w-xs">
              <p className="text-center text-gray-400 dark:text-white/60">
                Free and Open-Source Tailwind CSS Admin Dashboard Template
              </p>
            </div>
          </div>
        </div>
        <div className="fixed bottom-6 right-6 z-50 hidden sm:block">
          <ThemeToggleTwo />
        </div>
      </div>
    </div>
  );
}
