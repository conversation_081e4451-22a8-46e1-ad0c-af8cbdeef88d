import React from "react";
import { NavLink, useNavigate } from "react-router-dom";
import NotFount from "../components/assets/image/not-found.png";
import WNotound from "../components/assets/image/5203299.jpg"
const NotFound = () => {
  const navigate = useNavigate();
  return (
    <div className=" relative flex flex-col items-center justify-center min-h-screen p-6 overflow-hidden z-1 dark:bg-gray-900">
      <div className="mx-auto w-full max-w-[242px] text-center sm:max-w-[472px]">
        <h1 className="mb-8 font-bold text-gray-800 text-title-md dark:text-white/90 xl:text-title-2xl">
          404 ERROR
        </h1>
        <img
          src={NotFount}
          alt="404"
          className="hidden dark:block mx-auto mb-4"
          width={300}
          height={100}
        />
        <img
          src={WNotound}
          alt="404"
          className="dark:hidden mx-auto mb-4"
          width={300}
          height={100}
        />
        <p className="mb-2 text-base text-gray-700 dark:text-gray-400 sm:text-lg">
          OOOPS... SOMETHING GOES WRONG
        </p>
        <h2 className="mb-6 text-xl font-semibold text-gray-800 dark:text-white/90 sm:text-2xl">
          PAGE NOT FOUND
        </h2>
        <button
          onClick={() => navigate(-1)}
          className="inline-flex items-center justify-center gap-2.5 rounded-full bg-blue-700 px-8 py-3 text-sm font-medium text-white transition hover:bg-opacity-90"
        >
          GO BACK
        </button>
      </div>
    </div>
  );
};

export default NotFound;
