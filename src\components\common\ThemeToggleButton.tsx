import React from "react";
import { useTheme } from "../../context/ThemeContext";
import Iconify from "../icons";
export const ThemeToggleButton: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className="relative flex items-center justify-center text-gray-500 transition-colors bg-white border border-gray-200 rounded-full hover:text-dark-900 h-11 w-11 hover:bg-gray-100 hover:text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white"
      aria-label="Toggle theme"
    >
      {theme === "dark" ? (
        <div className="block">
          <Iconify icon="mingcute:moon-fill" width={20} height={20} />
        </div>
      ) : (
        <div className="block">
          <Iconify icon="mingcute:sun-fill" width={20} height={20} />
        </div>
      )}
    </button>
  );
};
