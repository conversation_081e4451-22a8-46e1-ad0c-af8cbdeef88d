import React from "react";
import UserBottonDrop from "../components/header/UserBottonDrop";
import { useSiderbar } from "../context/SidebarContext";
import AppHeader from "./AppHeader";
import Backdrop from "./Backdrop";
import { AppBar } from "@mui/material";
import { Outlet } from "react-router-dom";
import AppSidebar from "./AppSidebar";
const Dashboard = () => {
  const { isExpanded, isHovered, isMobileOpen } = useSiderbar();

  const mainContentMargin = isMobileOpen
    ? "ml-0"
    : isExpanded || isHovered
    ? "lg:ml-[290px]"
    : "lg:ml-[90px]";
  return (
    <div className="min-h-screen xl:flex dark:bg-gray-900">
      <AppSidebar />
      <Backdrop />
      {/* Main Content Wrapper */}
      <div className={`flex-1 flex flex-col min-h-screen ${mainContentMargin}`}>
        {/* Sticky Header */}
        <div className="sticky top-0 z-[999] bg-white dark:bg-gray-900 shadow-sm">
          <AppHeader />
        </div>

        {/* Scrollable Content Area */}
        <main className="flex-1 overflow-auto p-2 ">
          <div className="mx-auto p-6 rounded-md bg-gray-300/40 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
