import { useState } from "react";
import CheckBox from "./CheckBox";

const CheckBoxDemo = () => {
  const [states, setStates] = useState({
    default: false,
    success: false,
    warning: false,
    error: false,
    small: false,
    medium: false,
    large: false,
    disabled: false,
    disabledChecked: true,
  });

  const handleChange = (key: string, value: boolean) => {
    setStates(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="p-8 space-y-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Enhanced CheckBox Component</h1>
        
        {/* Variants Section */}
        <div className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Color Variants</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CheckBox
              id="default"
              label="Default Blue"
              checked={states.default}
              onChange={(checked) => handleChange("default", checked)}
              variant="default"
            />
            <CheckBox
              id="success"
              label="Success Green"
              checked={states.success}
              onChange={(checked) => handleChange("success", checked)}
              variant="success"
            />
            <CheckBox
              id="warning"
              label="Warning Amber"
              checked={states.warning}
              onChange={(checked) => handleChange("warning", checked)}
              variant="warning"
            />
            <CheckBox
              id="error"
              label="Error Red"
              checked={states.error}
              onChange={(checked) => handleChange("error", checked)}
              variant="error"
            />
          </div>
        </div>

        {/* Sizes Section */}
        <div className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Sizes</h2>
          <div className="space-y-4">
            <CheckBox
              id="small"
              label="Small Size"
              checked={states.small}
              onChange={(checked) => handleChange("small", checked)}
              size="sm"
            />
            <CheckBox
              id="medium"
              label="Medium Size (Default)"
              checked={states.medium}
              onChange={(checked) => handleChange("medium", checked)}
              size="md"
            />
            <CheckBox
              id="large"
              label="Large Size"
              checked={states.large}
              onChange={(checked) => handleChange("large", checked)}
              size="lg"
            />
          </div>
        </div>

        {/* States Section */}
        <div className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">States</h2>
          <div className="space-y-4">
            <CheckBox
              id="disabled"
              label="Disabled Unchecked"
              checked={states.disabled}
              onChange={(checked) => handleChange("disabled", checked)}
              disabled={true}
            />
            <CheckBox
              id="disabledChecked"
              label="Disabled Checked"
              checked={states.disabledChecked}
              onChange={(checked) => handleChange("disabledChecked", checked)}
              disabled={true}
            />
          </div>
        </div>

        {/* Features Section */}
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Features</h2>
          <div className="space-y-2 text-gray-600">
            <p>✨ <strong>Smooth Animations:</strong> Scale, rotate, and fade transitions</p>
            <p>🎨 <strong>Multiple Variants:</strong> Default, Success, Warning, Error</p>
            <p>📏 <strong>Three Sizes:</strong> Small, Medium, Large</p>
            <p>🎯 <strong>Interactive Effects:</strong> Hover, focus, and active states</p>
            <p>♿ <strong>Accessible:</strong> Screen reader friendly with proper ARIA</p>
            <p>🌙 <strong>Dark Mode:</strong> Automatic dark mode support</p>
            <p>💫 <strong>Ripple Effect:</strong> Subtle animation when checked</p>
            <p>🎪 <strong>Customizable:</strong> Easy to extend with custom styles</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckBoxDemo;
