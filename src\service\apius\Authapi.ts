import apiClient from "../apiClient";
import { Login, useResponse } from "../type/AuthUser";

export const Authapi = {
  login: async (Credential: Login): Promise<useResponse> => {
    const response = await apiClient.post("/login", Credential);
    const { data } = response;
    if(data.status === "success"){
      const userData = {
        user: data.data.user,
        token: data.data.token,
        token_type: data.data.token_type,
      }
      sessionStorage.setItem("user", JSON.stringify(userData));
    }
    //save token for session storage
    return data;
  },
  logout: async (): Promise<void> => {
    
    await apiClient.post("/logout");
  },

  checkAuth: (): boolean => {
    const user = sessionStorage.getItem("user");
    return !!user && JSON.parse(user).token;
  },
};
