import React from "react";
import { NavLink } from "react-router-dom";

interface DropdownItemProps {
  tag?: "a" | "button";
  href?: string;
  onClick?: () => void;
  onItemClick?: () => void;
  baseClassName?: string;
  className?: string;
  children: React.ReactNode;
}

export const DropdownIterm: React.FC<DropdownItemProps> = ({
  tag = "button",
  href,
  onClick,
  onItemClick,
  baseClassName = "block w-full text-left px-4 py-2 text-sm text-gold-mediem hover:bg-gold-low hover:text-gold-top",
  className = "",
  children,
}) => {
  const combinedClasses = `${baseClassName} ${className}`.trim();
  const handleClick = (event: React.MouseEvent) => {
    if (tag === "button") {
      event.preventDefault();
    }
    if (onClick) onClick();
    if (onItemClick) onItemClick();
  };
  if (tag === "a" && href) {
    return (
      <NavLink
        to={href}
        className={({ isActive }) =>
          `${baseClassName} ${className} ${
            isActive ? "bg-gold-mediem text-gray-100 font-semibold" : ""
          }`.trim()
        }
        onClick={handleClick}
      >
        {children}
      </NavLink>
    );
  }

  return (
    <button onClick={handleClick} className={combinedClasses}>
      {children}
    </button>
  );
};
