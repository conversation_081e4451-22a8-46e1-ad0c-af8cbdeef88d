import React, { useState, useEffect } from "react";
import { useNavigate, NavLink } from "react-router-dom";
import {
  Search,
  RefreshCw,
  AlertCircle,
  ChevronUp,
  ChevronDown,
  SquarePen,
  SquareX,
  TriangleAlert,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "../../components/table";
import Input from "../../components/form/input/InputField";
import Tooltip from "../../components/ui/button/Tooltip";
import { QRCodeCanvas } from "qrcode.react";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import { Snackbar ,Alert } from "@mui/material";
import { Branchus } from "../../service/apiBranch/Branchus";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from "@headlessui/react";
import {
  Branch as BranchType,
  Qecode,
  branchResponce,
} from "../../service/type/Branch";
import HasPermission from "../../provider/HasPermission";

const headCells = [
  {
    id: "id",
    label: "no",
    sortable: true,
    align: "left",
    minWidth: "30px",
  },
  {
    id: "branch_name",
    label: "Branch",
    sortable: true,
    align: "left",
    minWidth: "150px",
  },
  {
    id: "branch_address",
    label: "Branch Address",
    sortable: true,
    align: "left",
    minWidth: "120px",
  },
  {
    id: "qr_locations",
    label: "Qr Locations",
    sortable: false,
    align: "left",
    minWidth: "100px",
  },
  {
    id: "manager",
    label: "Manager",
    sortable: true,
    align: "left",
    minWidth: "120px",
  },
  {
    id: "action",
    label: "Actions",
    sortable: true,
    align: "left",
    minWidth: "120px",
  },
];

const Branch = () => {
  const [branches, setBranches] = useState<BranchType[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50);
  const [sortBy, setSortBy] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState("asc");
  const [selectedQR, setSelectedQR] = useState<string | null>(null);
  const [branchname, setbranchname] = useState("");
  const [selectedQRId, setSelectedQRId] = useState<string>("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [branchToDelete, setBranchToDelete] = useState<number | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"error" | "success">(
    "error"
  );
  const handleSnackbarClose = () => setSnackbarOpen(false);
  const navigate = useNavigate();
  type SortableBranchKeys = "id" | "branch_name" | "branch_address";

  const fetchBranches = async () => {
    try {
      const response: branchResponce = await Branchus.getAllBranches();
      setBranches(response.branches || []);
      // console.log("branch data", response.branches);
    } catch (err) {
      console.error("Failed to fetch branches:", err);
      setError("Failed to load branches. Please try again later.");
      setBranches([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBranches();
  }, []);

  const handleQRClick = (qr: Qecode) => {
    const qrData = { type: "branch", id: qr.id };
    setSelectedQR(JSON.stringify(qrData));
    setSelectedQRId(qr.id.toString());
  };

  const handleCloseModal = () => {
    setSelectedQR(null);
    setSelectedQRId("");
  };

  const handleExportPDF = async () => {
    const exportElement = document.getElementById("qr-export-template");
    if (!exportElement || !selectedQR) return;

    try {
      const canvas = await html2canvas(exportElement, {
        scale: 2, // Higher quality
        backgroundColor: null,
        useCORS: true,
        allowTaint: true,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4",
      });

      // Calculate dimensions to fit the page nicely
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const imgWidth = canvas.width;
      const imgHeight = canvas.height;
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
      const imgX = (pdfWidth - imgWidth * ratio) / 2;
      const imgY = (pdfHeight - imgHeight * ratio) / 2;

      pdf.addImage(
        imgData,
        "PNG",
        imgX,
        imgY,
        imgWidth * ratio,
        imgHeight * ratio
      );

      pdf.save(`QR_Code_${branchname}_${selectedQRId}.pdf`);
    } catch (error) {
      console.error("Error generating PDF:", error);
    }
  };

  const filteredData = branches.filter(
    (item) =>
      item.branch_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.branch_address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle sorting
  const handleSort = (cellId: SortableBranchKeys) => {
    const isCurrentSort = sortBy === cellId;
    setSortOrder(isCurrentSort && sortOrder === "asc" ? "desc" : "asc");
    setSortBy(cellId);
  };

  // Sort data
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortBy) return 0;

    const key = sortBy as SortableBranchKeys;
    const aValue = a[key];
    const bValue = b[key];

    if (key === "id") {
      const aNum = Number(aValue);
      const bNum = Number(bValue);
      return sortOrder === "asc" ? aNum - bNum : bNum - aNum;
    }

    const comparison = String(aValue).localeCompare(String(bValue));
    return sortOrder === "asc" ? comparison : -comparison;
  });

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedData.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  const handlEdit = (id: number) =>{
    navigate(`/branch/branchedit/${id}`)
  }
  const handleDelete = (id: number) =>{
    setBranchToDelete(id);
    setDeleteDialogOpen(true);
  }
  const handleDeleteconfime = async () => {
    if(branchToDelete === null) return;
    try{
      const response = await Branchus.delBranch(branchToDelete);
      setDeleteDialogOpen(false);
      setBranchToDelete(null);
      setSnackbarMessage(response.message);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      await fetchBranches();
    }catch(error){
      console.error("Error deleting branch:", error);
      setSnackbarMessage("Failed to delete branch");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      setDeleteDialogOpen(false);
      setBranchToDelete(null);
    }
  }
  return (
    <div className="max-w-full mx-auto">
      <div className="relative mb-6">
        <Input
          type="text"
          placeholder="Search branch name...."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="bg-white"
          icon={<Search className="h-5 w-5 text-gray-500 dark:text-gray-300" />}
        />
      </div>

      <div className="block xs:flex justify-between items-center mb-6">
        <h1 className="text-xl font-semibold mb-4">Branch Management</h1>
        <div className="flex items-end font-semibold">
          <NavLink to="/branch/branchadd">
            <button className="flex items-center h-10 justify-center rounded-lg border border-gray-300 bg-white px-3.5 py-2.5 text-gray-700 shadow-theme-xs hover:bg-gray-50 disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] text-sm">
              Add Branch
            </button>
          </NavLink>
        </div>
      </div>

      {error && (
        <div className="flex items-center p-4 mb-6 text-red-700 bg-red-100 rounded-lg">
          <AlertCircle className="w-5 h-5 mr-2" />
          {error}
        </div>
      )}

      {loading && (
        <div className="flex justify-center items-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading Branches.....</span>
        </div>
      )}

      {!loading && !error && (
        <div className="overflow-hidden rounded-md border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
          <div className="max-w-full overflow-x-auto">
            <div className="min-w-[1102px]">
              <Table>
                <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
                  <TableRow>
                    {headCells.map((headCell) => (
                      <TableCell
                        key={headCell.id}
                        className={`px-5 py-3 font-semibold text-gray-500 bg-gray-300/40 dark:bg-gray-800/50 text-${
                          headCell.align
                        } text-xs dark:text-gray-400 uppercase tracking-wider ${
                          headCell.sortable
                            ? "cursor-pointer hover:bg-gray-100/55 dark:hover:bg-gray-900/55 select-none"
                            : ""
                        }`}
                        style={{ minWidth: headCell.minWidth }}
                        onClick={
                          headCell.sortable
                            ? () =>
                                handleSort(headCell.id as SortableBranchKeys)
                            : undefined
                        }
                      >
                        <div className="flex items-center space-x-1">
                          <span>{headCell.label}</span>
                          {headCell.sortable && (
                            <div className="flex flex-col">
                              {sortBy === headCell.id ? (
                                sortOrder === "asc" ? (
                                  <ChevronUp className="w-3 h-3 text-blue-600" />
                                ) : (
                                  <ChevronDown className="w-3 h-3 text-blue-600" />
                                )
                              ) : (
                                <div className="flex flex-col opacity-30">
                                  <ChevronUp className="w-3 h-3 -mb-1" />
                                  <ChevronDown className="w-3 h-3" />
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                  {currentItems.length > 0 ? (
                    currentItems.map((item, index) => (
                      <TableRow
                        key={item.id}
                        className="dark:bg-gray-900 hover:bg-gray-100/50 dark:hover:bg-gray-900/50 transition-colors"
                      >
                        <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                          {index + 1}
                        </TableCell>
                        <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                          {item.branch_name}
                        </TableCell>
                        <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                          {item.branch_address}
                        </TableCell>
                        <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                          {item.qr_codes.map((qr) => (
                            <div
                              key={qr.id}
                              onClick={() => {
                                setbranchname(item.branch_name);
                                handleQRClick(qr);
                              }}
                            >
                              <QRCodeCanvas
                                value={JSON.stringify({
                                  type: "branch",
                                  id: qr.id,
                                })}
                                size={64}
                                style={{
                                  border: "3px solid white",
                                  cursor: "pointer",
                                }}
                              />
                            </div>
                          ))}
                        </TableCell>
                        <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                          {item.users.map((item) => (
                            <div>{item.name}</div>
                          ))}
                        </TableCell>
                        <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                          <div className=" flex flex-row gap-2 items-center justify-start">
                            {HasPermission(["edit branch"]) && (
                              <Tooltip content="Edite" style="primary">
                                <button className=" text-blue-800" onClick={() =>{setbranchname(item.branch_name); handlEdit(item.id)}}>
                                  <SquarePen size={20} />
                                </button>
                              </Tooltip>
                            )}
                            {HasPermission(["delete branch"]) && (
                              <Tooltip content="Delete" style="danger" >
                                <button className=" text-red-800" onClick={() => handleDelete(item.id)}>
                                  <SquareX size={20} />
                                </button>
                              </Tooltip>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={headCells.length}
                        className="px-5 py-4 text-center text-gray-500 dark:text-gray-400"
                      >
                        No branches found.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
          {totalPages >= 1 && (
            <div className="px-6 py-3 flex items-center justify-between border-t border-gray-100">
              <div className="flex-1 flex justify-between">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center h-10 justify-center rounded-lg border border-gray-300 bg-white px-3.5 py-2.5 text-gray-700 shadow-theme-xs hover:bg-gray-50 disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] text-sm"
                >
                  Previous
                </button>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center h-10 justify-center rounded-lg border border-gray-300 bg-white px-3.5 py-2.5 text-gray-700 shadow-theme-xs hover:bg-gray-50 disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] text-sm"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Hidden QR Export Template */}
      <div
        id="qr-export-template"
        className="fixed -top-[9999px] left-0 w-[450px] h-[600px] bg-gradient-to-br from-gold-mediem via-gold-mediem to-gold-mediem text-white"
        style={{ fontFamily: "Arial, sans-serif" }}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-yellow-400 to-orange-400 px-6 py-4 rounded-t-xl">
          <h1 className="text-gray-100 font-bold text-xl text-center">
            CHECK IN/OUT
          </h1>
        </div>
        <div className="absolute inset-0 pt-16">
          {/* Decorative circles */}
          <div className="absolute top-20 left-8 w-8 h-8 bg-gray-600 rounded-full opacity-30"></div>
          <div className="absolute top-32 left-16 w-6 h-6 bg-gray-600 rounded-full opacity-40"></div>
          <div className="absolute top-44 left-12 w-4 h-4 bg-gray-600 rounded-full opacity-50"></div>
          <div className="absolute top-20 right-8 w-6 h-6 bg-gray-600 rounded-full opacity-40"></div>
          <div className="absolute top-36 right-12 w-8 h-8 bg-gray-600 rounded-full opacity-30"></div>
          <div className="absolute top-52 right-16 w-5 h-5 bg-gray-600 rounded-full opacity-45"></div>
          <div className="absolute bottom-40 left-6 w-7 h-7 bg-gray-600 rounded-full opacity-35"></div>
          <div className="absolute bottom-56 left-20 w-5 h-5 bg-gray-600 rounded-full opacity-40"></div>
          <div className="absolute bottom-32 right-10 w-6 h-6 bg-gray-600 rounded-full opacity-30"></div>
          <div className="absolute bottom-48 right-6 w-4 h-4 bg-gray-600 rounded-full opacity-50"></div>
        </div>

        {/* Main Content */}
        <div className="relative z-10 flex flex-col items-center justify-center px-8 pt-12 pb-8">
          <div className="relative mb-10">
            <div className="w-96 h-96 bg-gray-100/50 rounded-full flex items-center justify-center shadow-2xl">
              <div className="w-48 h-48 bg-white rounded-lg flex items-center justify-center">
                {selectedQR && (
                  <QRCodeCanvas
                    value={selectedQR}
                    size={240}
                    level="M"
                    includeMargin={false}
                  />
                )}
              </div>
            </div>
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 px-4 py-1 rounded-full">
              <span className="text-white text-sm font-semibold">
                Branch - {branchname}
              </span>
            </div>
          </div>

          {/* Footer */}
          <div className="absolute bottom-3 left-0 right-0 flex justify-between items-center px-8 text-sm text-white/80">
            <div className="flex items-center">
              <span className="mr-2">Powered by</span>
              <div className="flex items-center">
                <span className="font-semibold">Vontex Dev</span>
              </div>
            </div>
            <div className="flex justify-between items-start p-4 text-sm">
              <div className="text-white/90">
                {new Date().toLocaleDateString("en-US", {
                  month: "2-digit",
                  day: "2-digit",
                  year: "2-digit",
                })}
                ,{" "}
                {new Date().toLocaleTimeString("en-US", {
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: true,
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Hidden QR Export Template */}
      <Dialog
        open={!!selectedQR}
        onClose={handleCloseModal}
        className="relative z-[999]"
      >
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-gray-500/75 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in"
        />
        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
            <DialogPanel
              transition
              className="relative w-[350px] h-[630px] transform overflow-hidden rounded-lg bg-gold-mediem text-left shadow-xl transition-all data-closed:translate-y-4 data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in sm:my-8 sm:w-full sm:max-w-lg data-closed:sm:translate-y-0 data-closed:sm:scale-95"
              style={{ fontFamily: "Arial, sans-serif" }}
            >
              <div className="bg-gradient-to-r from-yellow-400 to-orange-400 px-6 py-4 rounded-t-xl">
                <h1 className="text-gray-100 font-bold text-xl text-center">
                  CHECK IN/OUT
                </h1>
              </div>

              {/* Main Content */}
              <div className="relative z-10 flex flex-col items-center justify-center px-8 pt-12 pb-8">
                <div className="relative mb-10">
                  <div className="w-96 h-96 bg-gray-100/50 rounded-full flex items-center justify-center shadow-2xl">
                    <div className="w-48 h-48 bg-white rounded-lg flex items-center justify-center">
                      {selectedQR && (
                        <QRCodeCanvas
                          value={selectedQR}
                          size={240}
                          level="M"
                          includeMargin={false}
                        />
                      )}
                    </div>
                  </div>
                  <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 px-4 py-1 rounded-full">
                    <span className="text-white text-sm font-semibold">
                      Branch - {branchname}
                    </span>
                  </div>
                </div>

                {/* Footer */}
                <div className="absolute bottom-3 left-0 right-0 flex justify-between items-center px-8 text-[10px] sm:text-sm text-white/80">
                  <div className="flex items-center">
                    <span className="mr-2">Powered by</span>
                    <div className="flex items-center">
                      <span className="font-semibold">Vontex Dev</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-start p-4 text-[10px] sm:text-sm">
                    <div className="text-white/90">
                      {new Date().toLocaleDateString("en-US", {
                        month: "2-digit",
                        day: "2-digit",
                        year: "2-digit",
                      })}
                      ,{" "}
                      {new Date().toLocaleTimeString("en-US", {
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: true,
                      })}
                    </div>
                  </div>
                </div>
              </div>
              <div className=" mb-0 px-4 py-3 flex sm:flex-row justify-end items-center">
                <button
                  type="button"
                  data-autofocus
                  onClick={() => handleCloseModal()}
                  className="inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-xs mr-1  ring-gray-300  hover:bg-gray-50 sm:mt-0 sm:w-auto"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleExportPDF}
                  className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white ml-1 shadow-xs hover:bg-blue-500 sm:ml-3 sm:w-auto"
                >
                  Export PDF
                </button>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>

      {/*Dialog for delete brnahc */}
      <Dialog open={deleteDialogOpen} onClose={setDeleteDialogOpen} className=" relative z-[999]">
        <DialogBackdrop
           transition
           className="fixed inset-0 bg-gray-500/75 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in"
        />
        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <DialogPanel
              transition
              className="relative transform overflow-hidden rounded-lg bg-white dart:bg-gray-800 text-left shadow-xl transition-all data-closed:translate-y-4 data-closed:opacity-0 data-enter:duriation-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in sm:my-8 sm:w-full sm:max-w-lg data-closed:sm:translate-y-0 data-closed:sm:scale-95"
            >
              <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex size-12 shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:size-10">
                    <TriangleAlert className="size-6 text-red-600"/>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <DialogTitle as="h3" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      Data Deletion
                    </DialogTitle>
                    <div className="mt-2">
                      <p className="text-sm text-gray-900/50 dark:text-gray-100/50">
                        Are you sure you want to delete the data name {branchname} ? All of your data will be permanently removed.
                        This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-100/50 dark:bg-gray-800/50 px-5 py-3 sm:flex sm:flex-row-reverse sm:px-6">
              <button type="button" 
                onClick={()=>handleDeleteconfime()}
                className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-red-500 sm:ml-3 sm:w-auto"
                >
                Delete
              </button>
              <button 
                type="button" 
                onClick={()=>setDeleteDialogOpen(false)}
                className="mt-3 inline-flex w-full justify-center rounded-md bg-gray-100/50 px-3 py-2 text-sm font-semibold text-gray-800 dark:text-gray-100 shadow-xs right-1 ring-gray-300 ring-inset hover:bg-gray-100/50 dark:bg-gray-800/50 sm:mt-0 sm:w-auto "
              >
                Cancel
              </button>
              </div>
            </DialogPanel>
          </div>
        </div>
        
      </Dialog>
      <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={handleSnackbarClose}
        >
          <Alert onClose={handleSnackbarClose} severity={snackbarSeverity}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
    </div>
  );
};

export default Branch;
