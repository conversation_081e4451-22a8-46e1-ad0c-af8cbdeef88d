import { FC } from "react";

interface CheckBoxProps {
  label?: string;
  checked: boolean;
  className?: string;
  id?: string;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  variant?: "default" | "success" | "warning" | "error";
  size?: "sm" | "md" | "lg";
}

const CheckBox: FC<CheckBoxProps> = ({
  label,
  checked,
  className,
  id,
  onChange,
  disabled,
  variant = "default",
  size = "md",
}) => {
  // Size configurations
  const sizeConfig = {
    sm: {
      checkbox: "w-4 h-4",
      icon: "w-2.5 h-2.5",
      text: "text-xs",
      spacing: "space-x-2",
    },
    md: {
      checkbox: "w-5 h-5",
      icon: "w-3 h-3",
      text: "text-sm",
      spacing: "space-x-3",
    },
    lg: {
      checkbox: "w-6 h-6",
      icon: "w-4 h-4",
      text: "text-base",
      spacing: "space-x-3",
    },
  };

  // Color configurations
  const getVariantColors = () => {
    if (disabled) {
      return {
        checked: "bg-gray-400 border-gray-400",
        unchecked: "bg-gray-100 border-gray-300",
        text: "text-gray-400",
      };
    }

    const variants = {
      default: {
        checked: "bg-blue-600 border-blue-600 shadow-md shadow-blue-200",
        unchecked:
          "bg-white border-gray-300 hover:border-blue-400 group-hover:shadow-sm",
        text: "text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-white",
      },
      success: {
        checked: "bg-green-600 border-green-600 shadow-md shadow-green-200",
        unchecked:
          "bg-white border-gray-300 hover:border-green-400 group-hover:shadow-sm",
        text: "text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-white",
      },
      warning: {
        checked: "bg-amber-500 border-amber-500 shadow-md shadow-amber-200",
        unchecked:
          "bg-white border-gray-300 hover:border-amber-400 group-hover:shadow-sm",
        text: "text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-white",
      },
      error: {
        checked: "bg-red-600 border-red-600 shadow-md shadow-red-200",
        unchecked:
          "bg-white border-gray-300 hover:border-red-400 group-hover:shadow-sm",
        text: "text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-white",
      },
    };

    return variants[variant];
  };

  const colors = getVariantColors();
  const sizes = sizeConfig[size];
  return (
    <label
      className={`flex items-center ${
        sizes.spacing
      } group cursor-pointer select-none ${
        disabled ? "cursor-not-allowed opacity-60" : ""
      }`}
    >
      <div className="relative">
        <input
          id={id}
          type="checkbox"
          className="sr-only"
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
          disabled={disabled}
        />
        <div
          className={`
            ${
              sizes.checkbox
            } rounded-md border-2 transition-all duration-200 ease-in-out
            flex items-center justify-center transform
            ${checked ? colors.checked : colors.unchecked}
            ${
              !disabled &&
              "focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-20"
            }
            ${checked && !disabled ? "scale-105" : "scale-100"}
            hover:scale-105 active:scale-95
            ${className}
          `}
        >
          {/* Checkmark with enhanced animation */}
          <svg
            className={`
              ${sizes.icon} text-white transition-all duration-300 ease-out
              ${
                checked
                  ? "opacity-100 scale-100 rotate-0"
                  : "opacity-0 scale-50 rotate-12"
              }
            `}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>

          {/* Ripple effect on check */}
          {checked && !disabled && (
            <div className="absolute inset-0 rounded-md bg-white opacity-20 animate-ping"></div>
          )}
        </div>
      </div>
      {label && (
        <span
          className={`
            ${sizes.text} font-medium transition-colors duration-200
            ${colors.text}
          `}
        >
          {label}
        </span>
      )}
    </label>
  );
};

export default CheckBox;
