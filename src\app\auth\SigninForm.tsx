import React, { useEffect, useState } from "react";
import Input from "../../components/form/input/InputField";
import Label from "../../components/form/Label";
import Button from "../../components/ui/button/Button";
import { useNavigate } from "react-router-dom";
import { Snackbar, Alert } from "@mui/material";
import { Authapi } from "../../service/apius/Authapi";
import Iconify from "../../components/icons";
import Logo from "../../components/assets/image/vortexDev.jpg";
import Logonobg from "../../components/assets/image/logonobg.png";
import { useTheme } from "../../context/ThemeContext";
import { useAuth } from "../../provider/AuthContext";

interface ValidationErrors {
  codeid?: string;
  email?: string;
  password?: string;
}
const SigninForm = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const [codeid, setCodeId] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [isPasswordVisible, setPasswordVisibility] = useState<boolean>(false);
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"error" | "success">(
    "error"
  );

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { theme } = useTheme();
  const handleSnackbarClose = () => setSnackbarOpen(false);
  const validateForm = () => {
    const newErrors: ValidationErrors = {};

    // Code ID validation
    if (!codeid.trim()) {
      newErrors.codeid = "Code ID is required";
    } else if (codeid.length < 3) {
      newErrors.codeid = "Code ID must be at least 3 characters";
    }

    // Email validation
    if (!email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Password validation
    if (!password) {
      newErrors.password = "Password is required";
    } else if (password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!validateForm()) {
      setIsSubmitting(false);
      return;
    }
    try {
      const response = await Authapi.login({ codeid, email, password });
      setSnackbarMessage("Login successful");
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      console.log("Login Success:", response.data);

      // Call the login function from AuthContext instead
      setTimeout(() => {
        login();
        setSnackbarOpen(false);
      }, 2000);
      // No need for setTimeout and navigate here
    } catch (error) {
      setSnackbarMessage("Invalid credentials");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);

      setTimeout(() => {
        setSnackbarOpen(false);
      }, 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <div>
        <div className="flex flex-col ">
          <div className=" block justify-start right-0 ">
            {theme === "dark" ? (
              <img src={Logonobg} alt="logo" className="w-32" />
            ) : (
              <img src={Logo} alt="logo" className="w-32" />
            )}
          </div>
          <div className="justify-center flex-1 w-full max-w-md mx-auto">
            <h1 className="mb-2 font-semibold text-gold text-4xl dark:text-white/90 ">
              VortexDev
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Enter your credentials to sign in.
            </p>
            <form onSubmit={handleLogin} className="space-y-6 mt-5">
              <div>
                <Label>
                  Code ID <span className=" text-red-500">*</span>
                </Label>
                <Input
                  type="text"
                  placeholder="Enter your Code ID"
                  value={codeid}
                  onChange={(e) => {
                    setCodeId(e.target.value);
                    if (errors.codeid) {
                      setErrors((prevErrors) => ({
                        ...prevErrors,
                        codeid: "",
                      }));
                    }
                  }}
                  required
                />
                {errors.codeid && (
                  <p className="text-red-500 text-sm mt-1">{errors.codeid}</p>
                )}
              </div>
              <div>
                <Label>
                  Email <span className=" text-red-500">*</span>
                </Label>
                <Input
                  type="email"
                  placeholder="Enter your Email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (errors.email) {
                      setErrors((prevErrors) => ({
                        ...prevErrors,
                        email: "",
                      }));
                    }
                  }}
                  required
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                )}
              </div>
              <div>
                <Label>
                  Password <span className=" text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    type={isPasswordVisible ? "text" : "password"}
                    placeholder="Enter your Password"
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      if (errors.password) {
                        setErrors((prevErrors) => ({
                          ...prevErrors,
                          password: "",
                        }));
                      }
                    }}
                    required
                  />

                  <span
                    onClick={() => setPasswordVisibility(!isPasswordVisible)}
                    className="absolute cursor-pointer right-4 top-1/2 transform -translate-y-1/2"
                  >
                    <Iconify
                      icon={
                        isPasswordVisible
                          ? "mingcute:eye-fill"
                          : "mingcute:eye-close-fill"
                      }
                      color={isPasswordVisible ? "gray" : "gray"}
                      width={24}
                      height={24}
                    />
                  </span>
                </div>
                {errors.password && (
                  <p className="text-red-500 text-sm mt-1">{errors.password}</p>
                )}
              </div>
              <div>
                <Button
                  type="submit"
                  className="w-full bg-blue-700 font-bold"
                  size="sm"
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center">
                      <div className="w-5 h-5 border-t-2 border-b-2 border-white rounded-full animate-spin mr-2"></div>
                      Signing in...
                    </div>
                  ) : (
                    "Sign in"
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>

        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={handleSnackbarClose}
        >
          <Alert onClose={handleSnackbarClose} severity={snackbarSeverity}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </div>
    </div>
  );
};

export default SigninForm;
