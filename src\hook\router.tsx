import React from "react";
import { Route, Routes, Navigate, useLocation } from "react-router-dom";
import Dashboard from "../app/dashboard";
import Login from "../app/auth/index";
import App from "../layout/app";
import Logout from "../app/auth/Signout";
import NotFound from "../app/not-found";
import { useAuth } from "../provider/AuthContext";
import Branch from "../app/branch";
import { BranchRouter, Data } from "./BranchRouter";
import Branchadd from "../app/branch/branchadd";

// Protected Route wrapper component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuth();
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};
// Public Route wrapper component (redirects authenticated users away)
const PublicRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  if (isAuthenticated) {
    // If already authenticated, redirect to dashboard
    return <Navigate to="/dashboard" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

const Router = () => {
  return (
    <Routes>
      {/* Public routes */}
      <Route
        path="/login"
        element={
          <PublicRoute>
            <Login />
          </PublicRoute>
        }
      />
      <Route path="/logout" element={<Logout />} />
      {/* Protected routes with layout */}
      <Route
        path="/"
        element={
          <ProtectedRoute>
            <App />
          </ProtectedRoute>
        }
      >
        <Route index element={<Dashboard />} />
        <Route path="dashboard" element={<Dashboard />} />
        {/* <Route path="" element={<App />} /> */}
        {/* Router for branch */}
        {BranchRouter.map((item: Data) => (
          <Route key={item.path} path={item.path} element={item.element} />
        ))}
        <Route path="branch" element={<Branch />} />
        {/* end comment */}
        <Route path="profile" element={<div>Profile Page</div>} />
        <Route path="calendar" element={<div>Calendar Page</div>} />
        <Route path="form-elements" element={<div>Form Elements</div>} />
        <Route path="basic-tables" element={<div>Basic Tables</div>} />
      </Route>

      {/* 404 route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default Router;
