import React, { useEffect, useState } from "react";
import { NavLink } from "react-router-dom";
import { Dropdown } from "../ui/dropdown/Dropdown";
import { DropdownIterm } from "../ui/dropdown/DropdownIterm";
import Iconify from "../icons";
export default function UserBottonDrop() {
  const [isOpen, setIsOpen] = React.useState(false);
  const [user, setUser] = useState<{
    name: string;
    avatar: string;
    email: string;
  } | null>(null);
  useEffect(() => {
    const userstring = sessionStorage.getItem("user");
    if (userstring) {
      const userData = JSON.parse(userstring);
      const userID = userData?.user;
      setUser((prev) => ({
        ...prev,
        name: userID.name,
        avatar: userID.picture,
        email: userID.email,
      }));
    }
  }, []);

  function toggleDropdown(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
    e.stopPropagation();
    setIsOpen((prev) => !prev);
  }
  function closeDropdown() {
    setIsOpen(false);
  }
  return (
    <div className="relative">
      <button
        onClick={toggleDropdown}
        className="flex items-center text-gray-700 dark:text-gray-400 dropdown-toggle"
      >
        <span className="mr-3 overflow-hidden rounded-full h-11 w-11">
          {" "}
          <img
            src={`http://localhost:8000/storage/${user?.avatar}`}
            alt=""
            className="mr-3 overflow-hidden rounded-full h-11 w-11"
          />
        </span>
        <span className="block mr-1 font-medium text-theme-sm">
          {user?.name}
        </span>
        <svg
          className={`stroke-gray-500 dark:stroke-gray-400 transition-transform duration-200 ${
            isOpen ? "rotate-180" : ""
          }`}
          width="18"
          height="20"
          viewBox="0 0 18 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.3125 8.65625L9 13.3437L13.6875 8.65625"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
      <Dropdown
        isOpen={isOpen}
        onClose={closeDropdown}
        className="absolute right-0 mt-[17px] flex w-[260px] flex-col rounded-2xl border border-gray-200 bg-white/100 p-3 shadow-theme-lg dark:border-gray-800 dark:bg-gray-800/100"
      >
        <div>
          <span className="block font-medium text-gold text-theme-sm dark:text-gold">
            {user?.name}
          </span>
          <span className="mt-0.5 block text-[15px] text-gray-500 dark:text-gray-400">
            {user?.email}
          </span>
        </div>

        <ul className="flex flex-col gap-1 pt-4 pb-3 border-b border-gray-200 dark:border-gray-800 font-bold">
          <li>
            <DropdownIterm
              onItemClick={closeDropdown}
              tag="a"
              href="/dashboard"
              className="flex items-center gap-3 px-3 py-2  text-gray-700 rounded-lg group text-sm hover:bg-gold-mediem/55 hover:text-gray-100 dark:text-gray-400 dark:hover:bg-gold-mediem/55 dark:hover:text-gray-300"
            >
              <Iconify
                icon="material-symbols:account-circle"
                className="w-5 h-5 hover:text-gray-100 dark:hover:text-gray-300"
              />
              Edit profile
            </DropdownIterm>
          </li>
          <li>
            <DropdownIterm
              onItemClick={closeDropdown}
              tag="a"
              href="/profile"
              className="flex items-center gap-3 px-3 py-2  text-gray-700 rounded-lg group text-sm hover:bg-gold-mediem/55 hover:text-gray-100 dark:text-gray-400 dark:hover:bg-gold-mediem/55 dark:hover:text-gray-300"
            >
              <Iconify
                icon="material-symbols:settings"
                className="w-5 h-5 hover:text-gray-100 dark:hover:text-gray-300"
              />
              Account settings
            </DropdownIterm>
          </li>
          <li>
            <DropdownIterm
              onItemClick={closeDropdown}
              tag="a"
              href="/profile"
              className="flex items-center gap-3 px-3 py-2  text-gray-700 rounded-lg group text-sm hover:bg-gold-mediem/55 hover:text-gray-100 dark:text-gray-400 dark:hover:bg-gold-mediem/55 dark:hover:text-gray-300"
            >
              <Iconify
                icon="material-symbols:error"
                className="w-5 h-5 hover:text-gray-100 dark:hover:text-gray-300"
              />
              Support
            </DropdownIterm>
          </li>
        </ul>
        <NavLink
          to="/logout"
          className="flex items-center gap-3 px-3 py-2 font-bold  text-gray-700 rounded-lg group text-sm hover:bg-red-700/40 hover:text-gray-100 dark:text-gray-400 dark:hover:bg-red-700/40 dark:hover:text-gray-300"
        >
          <Iconify
            icon="material-symbols:lock-open-circle"
            className="w-5 h-5 hover:text-gray-100 dark:hover:text-gray-300"
          />
          Sign out
        </NavLink>
      </Dropdown>
    </div>
  );
}
