import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Autha<PERSON> } from "../../service/apius/Authapi";

export default function Signout() {
  const navigate = useNavigate();
  useEffect(() => {
    const fetchlogout = async () => {
      try {
        await Authapi.logout();
      } catch (err) {
        console.error("Error during logout:", err);
      } finally {
        localStorage.removeItem("userr");
        navigate("/login");
      }
    };
    fetchlogout();
  }, [navigate]);
  return <div>Signing out...</div>;
}
