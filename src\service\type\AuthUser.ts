export interface Login {
  codeid: string;
  email: string;
  password: string;
}
export interface Permission {
  id: string;
  name: string;
}
export interface Role {
  id: string;
  name: string;
  permissions: Permission[];
}
export interface Branch {
  id: number;
  branch_name: string;
  branch_address: string;
}
export interface Pivot {
  branch_id: number;
  user_id: number;
}
export interface User {
  id: number;
  codeid: string;
  name: string;
  email: string;
  email_verified_at: string | null;
  status: string;
  picture: string;
  created_at: string;
  updated_at: string;
  roles: Role[];
  permissions: Permission[];
  pivot: Pivot;
}

export interface useResponse {
  status: string;
  message: string;
  data: User[];
}

export interface CreateUserPayload {
  name: string;
  email: string;
  password: string;
  picture?: string;
  role_ids: number[];
  permission_ids: number[];
  branch_ids: number[];
}
