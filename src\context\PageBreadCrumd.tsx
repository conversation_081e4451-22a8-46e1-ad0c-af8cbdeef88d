import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";

export const PageBreadcrumb = () => {
  const location = useLocation();

  useEffect(() => {
    const pageTitle: Record<string, string> = {
      "/login": "Login",
      "/home": "Home",
      "/about": "About",
    };

    // Set the page title based on the current path
    document.title = pageTitle[location.pathname] || "VortexDev";
  }, [location.pathname]); // Add pathname as a dependency

  return null; // This component doesn't render any UI
};
