import { User } from "./AuthUser";

export interface BranchData {
  branch_name: string;
  branch_address: string;
  location_name: string;
  latitude: string;
  longitude: string;
  radius: number;
  user_ids: User[];
}
export interface Qecode {
  id: number;
  branchid: number;
  location_name: string;
  latitude: string;
  longitude: string;
  radius: number;
  created_at: string;
  updated_at: string;
}

export interface Branch {
  id: number;
  branch_name: string;
  branch_address: string;
  created_at: string;
  updated_at: string;
  qr_codes: Qecode[];
  users: User[];
}

export interface branchResponce {
  status: string;
  message: string;
  branches: Branch[];
}
