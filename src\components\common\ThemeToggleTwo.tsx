import { useTheme } from "../../context/ThemeContext";
import React from "react";
import Iconify from "../icons";
export default function ThemeToggleTwo() {
  const { theme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className="p-2 rounded-full bg-gray-900 text-white dark:text-gray-900 dark:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-indigo-300"
      aria-label="Toggle Theme"
    >
      {theme === "dark" ? (
        <div className="block">
          <Iconify icon="mingcute:moon-fill" width={20} height={20} />
        </div>
      ) : (
        <div className="block">
          <Iconify icon="mingcute:sun-fill" width={20} height={20} />
        </div>
      )}
    </button>
  );
}
