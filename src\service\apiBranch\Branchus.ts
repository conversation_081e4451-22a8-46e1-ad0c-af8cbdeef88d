import apiClient from "../apiClient";
import { BranchData, branchResponce } from "../type/Branch";

export const Branchus = {
  getAllBranches: async (): Promise<branchResponce> => {
    const response = await apiClient.get<branchResponce>("/branches");
    return response.data;
  },
  addBranch: async (Credential: BranchData): Promise<branchResponce> => {
    const response = await apiClient.post("/branches", Credential);
    return response.data;
  },
  upBranch: async (
    id: number,
    Credential: BranchData
  ): Promise<branchResponce> => {
    const response = await apiClient.put(`/branches/${id}`, Credential);
    return response.data;
  },
  delBranch: async (id: number): Promise<branchResponce> => {
    const response = await apiClient.delete(`/branches/${id}`);
    return response.data;
  },
};
